package com.joolun.cloud.weixin.api.service.impl;

import cn.hutool.core.bean.BeanUtil;
import com.alibaba.csp.sentinel.util.StringUtil;
import com.baomidou.mybatisplus.core.toolkit.ObjectUtils;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.joolun.cloud.common.core.constant.CommonConstants;
import com.joolun.cloud.common.core.util.R;
import com.joolun.cloud.weixin.api.mapper.AuctionCalendarMapper;
import com.joolun.cloud.weixin.api.service.*;
import com.joolun.cloud.weixin.api.util.ThirdSessionHolder;
import com.joolun.cloud.weixin.common.constant.WxConstants;
import com.joolun.cloud.weixin.common.dto.AuctionCalendarDTO;
import com.joolun.cloud.weixin.common.entity.*;
import com.joolun.cloud.weixin.common.enums.OrderInfoEnum;
import com.joolun.cloud.weixin.common.vo.AppointInfoVO;
import lombok.AllArgsConstructor;
import org.springframework.stereotype.Service;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONArray;
import cn.hutool.json.JSONUtil;

import java.time.LocalDateTime;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 档期时间表
 *
 * <AUTHOR>
 * @date 2022-02-19 14:33:35
 */
@Service
@AllArgsConstructor
public class AuctionCalendarServiceImpl extends ServiceImpl<AuctionCalendarMapper, AuctionCalendar> implements AuctionCalendarService {

    final private AppointRulesService appointRulesService;
    final private WxGrouponInfoService wxGrouponInfoService;
    final private WxGrouponUserService wxGrouponUserService;
    final private OrderInfoService orderInfoService;
    final private WxPageDeviseService wxPageDeviseService;

    @Override
    public List<AuctionCalendar> getListByCon(AuctionCalendarDTO auctionCalendarDTO) {
        auctionCalendarDTO.setEndTime(auctionCalendarDTO.getStartTime().plusDays(1));
        return this.baseMapper.getListByCon(auctionCalendarDTO);
    }


    @Override
    public R saveAppoint(AuctionCalendarDTO auctionCalendarDTO) {
        //检查用户是否有订单
        AppointRules appointRule = appointRulesService.getOne(Wrappers.<AppointRules>lambdaQuery()
                .eq(AppointRules::getPageId, auctionCalendarDTO.getPageId())
                .eq(AppointRules::getDelFlag, CommonConstants.STATUS_NORMAL));
        if (ObjectUtils.isNull(appointRule)) {
            return R.failed("非法请求");
        }
        if (StringUtil.isBlank(appointRule.getRoomIdList()) || StringUtil.isBlank(appointRule.getPayPageId())) {
            return R.failed("选档设置不正确，请咨询商家");
        }

        List<String> collect = Arrays.stream(appointRule.getRoomIdList().split(",")).collect(Collectors.toList());
        auctionCalendarDTO.setRoomIdList(collect);
        boolean ruleFlag = checkRule(appointRule, auctionCalendarDTO);
        if (!ruleFlag) {
            return R.failed("不满足选档条件，请咨询商家");
        }

        //检查是否还有档期
        //解决UnsupportedOperationException
        List<AuctionCalendar> list = this.baseMapper.getListByDateTime(auctionCalendarDTO);
        
        // 获取页面配置信息
        WxPageDevise wxPageDevise = wxPageDeviseService.getById(auctionCalendarDTO.getPageId());
        if (ObjectUtils.isNull(wxPageDevise) || ObjectUtils.isNull(wxPageDevise.getPageComponent())) {
            return R.failed("页面配置不存在");
        }
        
        // 从页面组件中获取auctionTimeComponent配置
        JSONObject pageComponent = wxPageDevise.getPageComponent();
        JSONObject auctionTimeComponent = null;
        
        // 遍历组件列表查找auctionTimeComponent
        for (Object component : pageComponent.getJSONArray("componentsList")) {
            JSONObject comp = (JSONObject) component;
            if ("auctionTimeComponent".equals(comp.getStr("componentName"))) {
                auctionTimeComponent = comp.getJSONObject("data");
                break;
            }
        }
        
        if (ObjectUtils.isNull(auctionTimeComponent)) {
            return R.failed("档期组件配置不存在");
        }
        
        // 获取最大可预约次数
        Integer maxSelectCount = auctionTimeComponent.getInt("maxSelectCount", 1);
        
        // 获取当前预约的日期和时间段
        String appointDate = auctionCalendarDTO.getStartTime().toLocalDate().toString();
        String timeSlot = auctionCalendarDTO.getStartTime().toLocalTime().toString().substring(0, 5) + "-" 
                + auctionCalendarDTO.getEndTime().toLocalTime().toString().substring(0, 5);
        
        // 检查该日期时段的已预约数量
        long appointCount = list.stream()
                .filter(item -> item.getStartTime().toLocalDate().toString().equals(appointDate) 
                        && item.getRoomId().equals(auctionCalendarDTO.getRoomId()))
                .count();
        
        // 检查是否有特殊处理的预约限制
        JSONObject scheduledData = auctionTimeComponent.getJSONObject("scheduledData");
        if (ObjectUtils.isNotNull(scheduledData) && scheduledData.containsKey(appointDate)) {
            JSONObject dateConfig = scheduledData.getJSONObject(appointDate);
            if (ObjectUtils.isNotNull(dateConfig) && dateConfig.containsKey(timeSlot)) {
                // 获取特殊配置的可预约次数
                Integer specialLimit = dateConfig.getInt(timeSlot);
                
                // 如果特殊限制为0，表示不可预约
                if (specialLimit == 0) {
                    return R.failed("该时段不可预约");
                }
                
                // 如果有特殊限制且已达到限制，则不可预约
                if (specialLimit > 0 && appointCount >= specialLimit) {
                    return R.failed("该时段预约已满");
                }
                
                // 如果有特殊限制且未达到限制，则可以预约
            } else {
                // 没有特殊配置，使用默认maxSelectCount
                if (appointCount >= maxSelectCount) {
                    return R.failed("该档期在您点击的瞬间已被预约");
//                    return R.failed("该时段预约已达上限");
                }
            }
        } else {
            // 没有特殊配置，使用默认maxSelectCount
            if (appointCount >= maxSelectCount) {
                return R.failed("该时段预约已达上限");
            }
        }
        
        //随便拿取第一位
        AuctionCalendar auctionCalendar = new AuctionCalendar();
        BeanUtil.copyProperties(auctionCalendarDTO, auctionCalendar);
        OrderInfo order = orderInfoService.getOne(Wrappers.<OrderInfo>lambdaQuery()
                .eq(OrderInfo::getPageId, appointRule.getPayPageId())
                .eq(OrderInfo::getUserId, ThirdSessionHolder.getWxUserId())
//                在这里加上这个支付记录ID 只有已支付的才有这个ID. author:qian5201314
                .isNotNull(OrderInfo::getTransactionId)
                .eq(OrderInfo::getDelFlag, CommonConstants.STATUS_NORMAL));

        String statusWaitTakeValue = OrderInfoEnum.STATUS_12.getValue();
        if (!order.getStatus().equals(statusWaitTakeValue)) {
            order.setStatus(statusWaitTakeValue);
            orderInfoService.updateById(order);
        }
        auctionCalendar.setOrderId(order.getId());
        auctionCalendar.setUserId(ThirdSessionHolder.getWxUserId());
//        auctionCalendar.setRoomId(roomIdList.get(0));
        auctionCalendar.setRoomId(auctionCalendarDTO.getRoomId());
        return R.ok(this.save(auctionCalendar));
//		return R.ok();
    }

    /**
     * 检查是否满足参与规则
     *
     * @param appointRules
     * @param auctionCalendarDTO
     * @return
     */
    private boolean checkRule(AppointRules appointRules, AuctionCalendarDTO auctionCalendarDTO) {
        String type = appointRules.getType();
        if (StringUtil.isBlank(appointRules.getPayPageId())) {
            return false;
        }
        OrderInfo order = orderInfoService.getOne(Wrappers.<OrderInfo>lambdaQuery()
                .eq(OrderInfo::getPageId, appointRules.getPayPageId())
                .eq(OrderInfo::getUserId, ThirdSessionHolder.getWxUserId())
                .eq(OrderInfo::getIsPay, CommonConstants.YES)
                .eq(OrderInfo::getDelFlag, CommonConstants.NO));
        if (ObjectUtils.isNull(order)) {
            return false;
        }
        //拼团成功后选档
        if (WxConstants.APPOINT_RULE_TYPE_0.equals(type)) {
            //查询到完成拼团的用户
            WxGrouponInfo one = wxGrouponInfoService.getOne(Wrappers.<WxGrouponInfo>lambdaQuery().eq(WxGrouponInfo::getPageId, appointRules.getPayPageId()));
            wxGrouponUserService.getOne(Wrappers.<WxGrouponUser>lambdaQuery()
                    .eq(WxGrouponUser::getUserId, ThirdSessionHolder.getWxUserId())
                    .eq(WxGrouponUser::getGrouponId, one.getId())
                    .eq(WxGrouponUser::getStatus, WxConstants.SPELL_GROUP_USER_STATUS_SUCCESS)
                    .eq(WxGrouponUser::getTrueFlag, WxConstants.GROUP_USER_IS_TRUE));
            if (ObjectUtils.isNotNull(one)) {
                return true;
            }
        }
        return false;
    }

    @Override
    public AppointInfoVO getAppointInfo(AuctionCalendarDTO auctionCalendarDTO) {
        AppointInfoVO res = new AppointInfoVO();
        //得到店铺信息
        AppointRules appointRules = appointRulesService.getOne(Wrappers.<AppointRules>lambdaQuery().eq(AppointRules::getPageId, auctionCalendarDTO.getPageId()));
        //参与
        boolean ruleFlag = checkRule(appointRules, auctionCalendarDTO);
        res.setAddFlag(ruleFlag);
        //重选
        if (CommonConstants.YES.equals(appointRules.getChangeFlag())) {
            //允许 且在时间范围内可以换挡
            Integer hour = appointRules.getChangeTime();
            LocalDateTime permitTime = appointRules.getStartTime().minusHours(hour);
            if (LocalDateTime.now().isAfter(permitTime)) {
                res.setChangeFlag(true);
            }
        } else {
            res.setChangeFlag(false);
        }

        appointRules.setPageId(auctionCalendarDTO.getPageId());
        //店铺场地设置
        List<ShopInfo> shopInfoByPageId = appointRulesService.getShopInfoByPageId(appointRules);
        shopInfoByPageId.removeAll(Collections.singleton(null));
        if (!shopInfoByPageId.isEmpty()) {
            res.setShopList(shopInfoByPageId);
        }

        //得到用户约档信息
        OrderInfo one = orderInfoService.getOne(Wrappers.<OrderInfo>lambdaQuery()
                .eq(OrderInfo::getPageId, appointRules.getPayPageId())
                .eq(OrderInfo::getDelFlag, CommonConstants.NO)
                .eq(OrderInfo::getIsPay, CommonConstants.YES)
                .eq(OrderInfo::getUserId, ThirdSessionHolder.getWxUserId())
        );
        if (ObjectUtils.isNotNull(one)) {
            AuctionCalendar appoint = this.getOne(Wrappers.<AuctionCalendar>lambdaQuery()
                    .eq(AuctionCalendar::getOrderId, one.getId())
                    .eq(AuctionCalendar::getUserId, ThirdSessionHolder.getWxUserId()));
            res.setAppoint(appoint);
        }
        return res;
    }

    @Override
    public R resetAppoint(AuctionCalendarDTO auctionCalendarDTO) {
        auctionCalendarDTO.setUserId(ThirdSessionHolder.getWxUserId());
        //得到店铺信息
        AppointRules appointRules = appointRulesService.getOne(Wrappers.<AppointRules>lambdaQuery().eq(AppointRules::getPageId, auctionCalendarDTO.getPageId()));

        if (CommonConstants.YES.equals(appointRules.getChangeFlag())) {
            //拿到更改次数
            Integer appointNum = this.getAppointNum(auctionCalendarDTO, appointRules);
            if (ObjectUtils.isNotNull(appointRules.getChangeNum()) && appointNum >= appointRules.getChangeNum()) {
                return R.failed("次数已到限制，不可更改");
            }
            //检查是否有档期并删除
            AuctionCalendar appoint = this.getOne(Wrappers.<AuctionCalendar>lambdaQuery()
                    .eq(AuctionCalendar::getId, auctionCalendarDTO.getAppointId())
                    .eq(AuctionCalendar::getUserId, auctionCalendarDTO.getUserId())
                    .eq(AuctionCalendar::getDelFlag, CommonConstants.STATUS_NORMAL));
            if (ObjectUtils.isNull(appoint)) {
                return R.failed("该档期在您点击的瞬间已被预约");
            }
            //检查能否在规则时间内选档
            Integer hour = appointRules.getChangeTime();
            LocalDateTime permitTime = appoint.getStartTime().minusHours(hour);
            if (LocalDateTime.now().isAfter(permitTime)) {
                return R.failed("不在可修改时间内，不可更改");
            }
            String orderId = appoint.getOrderId();
            OrderInfo orderInfo = orderInfoService.getById(orderId);
            String statusWaitTakeValue = OrderInfoEnum.STATUS_12.getValue();
            if (!orderInfo.getStatus().equals(statusWaitTakeValue)) {
                orderInfo.setStatus(statusWaitTakeValue);
                orderInfoService.updateById(orderInfo);
            }
            //删除旧档期
            this.removeById(appoint.getId());
            //添加新档期
            return this.saveAppoint(auctionCalendarDTO);
        }
        return R.failed("选档设置不正确，请咨询商家");
    }

    @Override
    public Integer getAppointNum(AuctionCalendarDTO auctionCalendarDTO, AppointRules appointRules) {
        OrderInfo order = orderInfoService.getOne(Wrappers.<OrderInfo>lambdaQuery()
                .eq(OrderInfo::getPageId, appointRules.getPayPageId())
                .eq(OrderInfo::getUserId, ThirdSessionHolder.getWxUserId())
                .eq(OrderInfo::getIsPay, CommonConstants.YES)
                .eq(OrderInfo::getDelFlag, CommonConstants.NO));
        auctionCalendarDTO.setOrderId(order.getId());
        return this.baseMapper.getAppointNum(auctionCalendarDTO);
    }
}